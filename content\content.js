// FocusGuard Pro - Content Script
// Phase 1: Basic content monitoring and blocking support

class FocusGuardContent {
  constructor() {
    this.isBlocked = false;
    this.currentDomain = '';
    this.startTime = Date.now();
    this.init();
  }

  init() {
    // Get current domain
    this.currentDomain = this.extractDomain(window.location.href);

    // Check if current site should be blocked
    this.checkSiteStatus();

    // Set up monitoring
    this.setupMonitoring();

    // Listen for messages from background script
    this.setupMessageListener();

    console.log('FocusGuard Content Script initialized for:', this.currentDomain);
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace(/^www\./, '');
    } catch (error) {
      return window.location.hostname.replace(/^www\./, '');
    }
  }

  async checkSiteStatus() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'checkSiteStatus',
        url: window.location.href
      });

      if (response && response.isBlocked) {
        this.isBlocked = true;
        this.handleBlockedSite();
      }
    } catch (error) {
      console.error('Error checking site status:', error);
    }
  }

  handleBlockedSite() {
    // If the site is blocked, the background script should have already redirected
    // This is a fallback in case the redirect didn't work
    if (this.isBlocked && !window.location.href.includes('blocked.html')) {
      const blockedPageUrl = chrome.runtime.getURL('blocked/blocked.html') +
        '?blocked=' + encodeURIComponent(window.location.href);
      window.location.href = blockedPageUrl;
    }
  }

  setupMonitoring() {
    // Track time spent on site (for future analytics)
    this.trackTimeSpent();

    // Monitor for navigation changes (for SPAs)
    this.monitorNavigation();

    // Monitor for focus/blur events
    this.monitorFocus();

    // Note: Real-time content filtering has been removed per user request
    // Only domain-based blocking and search query filtering remain active
  }

  // Note: Real-time content filtering methods have been removed
  // The extension now only performs domain-based blocking and search query filtering

  trackTimeSpent() {
    // Track time spent on the current page
    let lastActiveTime = Date.now();
    let totalTime = 0;
    let isActive = !document.hidden;

    const updateTime = () => {
      const now = Date.now();
      if (isActive && document.hasFocus()) {
        const timeSpent = now - lastActiveTime;
        totalTime += timeSpent;
      }
      lastActiveTime = now;
    };

    // Update time on various events
    document.addEventListener('visibilitychange', () => {
      updateTime();
      isActive = !document.hidden;
      lastActiveTime = Date.now();
    });

    window.addEventListener('focus', () => {
      updateTime();
      isActive = true;
      lastActiveTime = Date.now();
    });

    window.addEventListener('blur', () => {
      updateTime();
      isActive = false;
    });

    // Send time data periodically (every 30 seconds)
    setInterval(() => {
      if (totalTime > 0) {
        this.sendTimeData(totalTime);
        totalTime = 0; // Reset counter
      }
    }, 30000);

    // Send final time data when page unloads
    window.addEventListener('beforeunload', () => {
      updateTime();
      if (totalTime > 0) {
        this.sendTimeData(totalTime);
      }
    });
  }

  async sendTimeData(timeSpent) {
    try {
      // Convert milliseconds to minutes
      const timeInMinutes = timeSpent / (1000 * 60);

      await chrome.runtime.sendMessage({
        action: 'updateTimeSpent',
        domain: this.currentDomain,
        timeSpent: timeInMinutes
      });
    } catch (error) {
      console.error('Error sending time data:', error);
    }
  }

  monitorNavigation() {
    // Monitor for URL changes in single-page applications
    let currentUrl = window.location.href;

    const checkUrlChange = () => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        const newDomain = this.extractDomain(currentUrl);

        if (newDomain !== this.currentDomain) {
          this.currentDomain = newDomain;
          this.checkSiteStatus();
        }
      }
    };

    // Check for URL changes periodically
    setInterval(checkUrlChange, 1000);

    // Also listen for popstate events
    window.addEventListener('popstate', checkUrlChange);
  }

  monitorFocus() {
    // Monitor when user focuses/unfocuses the tab
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // Tab lost focus
        this.onTabBlur();
      } else {
        // Tab gained focus
        this.onTabFocus();
      }
    });
  }

  onTabFocus() {
    // Re-check site status when tab gains focus
    this.checkSiteStatus();
  }

  onTabBlur() {
    // Could be used for analytics or other features
    console.log('Tab lost focus');
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open
    });
  }

  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'checkIfBlocked':
        sendResponse({
          isBlocked: this.isBlocked,
          domain: this.currentDomain
        });
        break;

      case 'forceRecheck':
        this.checkSiteStatus();
        sendResponse({ success: true });
        break;

      case 'getPageInfo':
        sendResponse({
          domain: this.currentDomain,
          url: window.location.href,
          title: document.title,
          isBlocked: this.isBlocked
        });
        break;

      default:
        sendResponse({ error: 'Unknown action' });
    }
  }

  // Utility method to inject blocking overlay (for future use)
  injectBlockingOverlay() {
    // This could be used for soft blocking instead of redirects
    const overlay = document.createElement('div');
    overlay.id = 'focusguard-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(102, 126, 234, 0.95);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      backdrop-filter: blur(10px);
    `;

    overlay.innerHTML = `
      <div style="text-align: center; max-width: 400px; padding: 40px;">
        <div style="font-size: 48px; margin-bottom: 20px;">🛡️</div>
        <h1 style="font-size: 24px; margin-bottom: 16px;">Site Blocked</h1>
        <p style="font-size: 16px; margin-bottom: 24px; opacity: 0.9;">
          This website has been blocked by FocusGuard Pro to help you stay focused.
        </p>
        <button id="focusguard-close" style="
          background: rgba(255,255,255,0.2);
          border: 2px solid rgba(255,255,255,0.3);
          color: white;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
        ">Go Back</button>
      </div>
    `;

    document.body.appendChild(overlay);

    // Add close functionality
    document.getElementById('focusguard-close').addEventListener('click', () => {
      window.history.back();
    });
  }

  // Method to remove blocking overlay
  removeBlockingOverlay() {
    const overlay = document.getElementById('focusguard-overlay');
    if (overlay) {
      overlay.remove();
    }
  }

  // Note: Page content keyword scanning has been removed
  // Keywords are now only checked in search queries, not page content

  // Note: Real-time adult content detection has been removed
  // Adult content blocking now only works at the domain level (background script)

  // Note: All real-time content scanning methods have been removed
  // Content filtering is now limited to:
  // 1. Domain-based blocking (handled by background script)
  // 2. Search query filtering (handled by background script)
}

// Initialize content script only if not already initialized
if (!window.focusGuardInitialized) {
  window.focusGuardInitialized = true;

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new FocusGuardContent();
    });
  } else {
    new FocusGuardContent();
  }
}
