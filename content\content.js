// FocusGuard Pro - Content Script
// Phase 1: Basic content monitoring and blocking support

class FocusGuardContent {
  constructor() {
    this.isBlocked = false;
    this.currentDomain = '';
    this.startTime = Date.now();
    this.init();
  }

  init() {
    // Get current domain
    this.currentDomain = this.extractDomain(window.location.href);

    // Check if current site should be blocked
    this.checkSiteStatus();

    // Set up monitoring
    this.setupMonitoring();

    // Listen for messages from background script
    this.setupMessageListener();

    console.log('FocusGuard Content Script initialized for:', this.currentDomain);
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace(/^www\./, '');
    } catch (error) {
      return window.location.hostname.replace(/^www\./, '');
    }
  }

  async checkSiteStatus() {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'checkSiteStatus',
        url: window.location.href
      });

      if (response && response.isBlocked) {
        this.isBlocked = true;
        this.handleBlockedSite();
      }
    } catch (error) {
      console.error('Error checking site status:', error);
    }
  }

  handleBlockedSite() {
    // If the site is blocked, the background script should have already redirected
    // This is a fallback in case the redirect didn't work
    if (this.isBlocked && !window.location.href.includes('blocked.html')) {
      const blockedPageUrl = chrome.runtime.getURL('blocked/blocked.html') +
        '?blocked=' + encodeURIComponent(window.location.href);
      window.location.href = blockedPageUrl;
    }
  }

  setupMonitoring() {
    // Track time spent on site (for future analytics)
    this.trackTimeSpent();

    // Monitor for navigation changes (for SPAs)
    this.monitorNavigation();

    // Monitor for focus/blur events
    this.monitorFocus();

    // Phase 4: Content filtering monitoring
    this.setupContentFiltering();
  }

  // Phase 4: Setup content filtering monitoring
  setupContentFiltering() {
    // Wait for page to fully load before scanning
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.performContentScan();
      });
    } else {
      this.performContentScan();
    }

    // Monitor for dynamic content changes
    this.observeContentChanges();
  }

  // Phase 4: Perform content scanning
  async performContentScan() {
    try {
      // Check for adult content
      const hasAdultContent = await this.detectAdultContent();
      if (hasAdultContent) {
        return; // Stop further processing if adult content found
      }

      // Check for blocked keywords
      await this.checkForBlockedKeywords();
    } catch (error) {
      console.error('Error performing content scan:', error);
    }
  }

  // Phase 4: Observe content changes for dynamic sites
  observeContentChanges() {
    // Create a mutation observer to watch for content changes
    const observer = new MutationObserver((mutations) => {
      let shouldRescan = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if significant content was added
          for (const node of mutation.addedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node;
              if (element.tagName === 'IMG' ||
                element.tagName === 'A' ||
                element.tagName === 'DIV' ||
                element.tagName === 'SECTION') {
                shouldRescan = true;
                break;
              }
            }
          }
        }
      });

      if (shouldRescan) {
        // Debounce rescanning to avoid excessive checks
        clearTimeout(this.rescanTimeout);
        this.rescanTimeout = setTimeout(() => {
          this.performContentScan();
        }, 1000);
      }
    });

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Store observer for cleanup
    this.contentObserver = observer;
  }

  trackTimeSpent() {
    // Track time spent on the current page
    let lastActiveTime = Date.now();
    let totalTime = 0;
    let isActive = !document.hidden;

    const updateTime = () => {
      const now = Date.now();
      if (isActive && document.hasFocus()) {
        const timeSpent = now - lastActiveTime;
        totalTime += timeSpent;
      }
      lastActiveTime = now;
    };

    // Update time on various events
    document.addEventListener('visibilitychange', () => {
      updateTime();
      isActive = !document.hidden;
      lastActiveTime = Date.now();
    });

    window.addEventListener('focus', () => {
      updateTime();
      isActive = true;
      lastActiveTime = Date.now();
    });

    window.addEventListener('blur', () => {
      updateTime();
      isActive = false;
    });

    // Send time data periodically (every 30 seconds)
    setInterval(() => {
      if (totalTime > 0) {
        this.sendTimeData(totalTime);
        totalTime = 0; // Reset counter
      }
    }, 30000);

    // Send final time data when page unloads
    window.addEventListener('beforeunload', () => {
      updateTime();
      if (totalTime > 0) {
        this.sendTimeData(totalTime);
      }
    });
  }

  async sendTimeData(timeSpent) {
    try {
      // Convert milliseconds to minutes
      const timeInMinutes = timeSpent / (1000 * 60);

      await chrome.runtime.sendMessage({
        action: 'updateTimeSpent',
        domain: this.currentDomain,
        timeSpent: timeInMinutes
      });
    } catch (error) {
      console.error('Error sending time data:', error);
    }
  }

  monitorNavigation() {
    // Monitor for URL changes in single-page applications
    let currentUrl = window.location.href;

    const checkUrlChange = () => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        const newDomain = this.extractDomain(currentUrl);

        if (newDomain !== this.currentDomain) {
          this.currentDomain = newDomain;
          this.checkSiteStatus();
        }
      }
    };

    // Check for URL changes periodically
    setInterval(checkUrlChange, 1000);

    // Also listen for popstate events
    window.addEventListener('popstate', checkUrlChange);
  }

  monitorFocus() {
    // Monitor when user focuses/unfocuses the tab
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // Tab lost focus
        this.onTabBlur();
      } else {
        // Tab gained focus
        this.onTabFocus();
      }
    });
  }

  onTabFocus() {
    // Re-check site status when tab gains focus
    this.checkSiteStatus();
  }

  onTabBlur() {
    // Could be used for analytics or other features
    console.log('Tab lost focus');
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open
    });
  }

  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'checkIfBlocked':
        sendResponse({
          isBlocked: this.isBlocked,
          domain: this.currentDomain
        });
        break;

      case 'forceRecheck':
        this.checkSiteStatus();
        sendResponse({ success: true });
        break;

      case 'getPageInfo':
        sendResponse({
          domain: this.currentDomain,
          url: window.location.href,
          title: document.title,
          isBlocked: this.isBlocked
        });
        break;

      default:
        sendResponse({ error: 'Unknown action' });
    }
  }

  // Utility method to inject blocking overlay (for future use)
  injectBlockingOverlay() {
    // This could be used for soft blocking instead of redirects
    const overlay = document.createElement('div');
    overlay.id = 'focusguard-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(102, 126, 234, 0.95);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      backdrop-filter: blur(10px);
    `;

    overlay.innerHTML = `
      <div style="text-align: center; max-width: 400px; padding: 40px;">
        <div style="font-size: 48px; margin-bottom: 20px;">🛡️</div>
        <h1 style="font-size: 24px; margin-bottom: 16px;">Site Blocked</h1>
        <p style="font-size: 16px; margin-bottom: 24px; opacity: 0.9;">
          This website has been blocked by FocusGuard Pro to help you stay focused.
        </p>
        <button id="focusguard-close" style="
          background: rgba(255,255,255,0.2);
          border: 2px solid rgba(255,255,255,0.3);
          color: white;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
        ">Go Back</button>
      </div>
    `;

    document.body.appendChild(overlay);

    // Add close functionality
    document.getElementById('focusguard-close').addEventListener('click', () => {
      window.history.back();
    });
  }

  // Method to remove blocking overlay
  removeBlockingOverlay() {
    const overlay = document.getElementById('focusguard-overlay');
    if (overlay) {
      overlay.remove();
    }
  }

  // Phase 4: Keyword blocking functionality
  async checkForBlockedKeywords() {
    try {
      // Get blocked keywords from background
      const response = await chrome.runtime.sendMessage({
        action: 'getBlockedKeywords'
      });

      if (response && response.keywords) {
        const pageText = document.body.innerText.toLowerCase();
        const pageTitle = document.title.toLowerCase();

        for (const keyword of response.keywords) {
          if (pageText.includes(keyword) || pageTitle.includes(keyword)) {
            console.log('Blocked keyword found on page:', keyword);
            this.handleBlockedContent('keyword', keyword);
            return true;
          }
        }
      }
    } catch (error) {
      console.error('Error checking for blocked keywords:', error);
    }

    return false;
  }

  // Phase 4: Adult content detection
  async detectAdultContent() {
    try {
      // Check if adult content blocking is enabled
      const response = await chrome.runtime.sendMessage({
        action: 'getContentFilteringStatus'
      });

      if (!response || !response.contentFiltering.enableAdultContentBlocking) {
        return false;
      }

      // Whitelist trusted financial and business domains
      const trustedDomains = [
        'paystack.com', 'stripe.com', 'paypal.com', 'square.com',
        'flutterwave.com', 'interswitch.com', 'remita.net',
        'bank.com', 'banking.com', 'finance.com', 'fintech.com',
        'visa.com', 'mastercard.com', 'americanexpress.com',
        'chase.com', 'wellsfargo.com', 'bankofamerica.com',
        'citibank.com', 'hsbc.com', 'barclays.com',
        'gtbank.com', 'zenithbank.com', 'accessbank.com',
        'firstbank.com', 'ubagroup.com', 'sterlingbank.com'
      ];

      // Skip adult content detection for trusted domains
      const currentDomain = window.location.hostname.replace(/^www\./, '');
      if (trustedDomains.some(trusted => currentDomain.includes(trusted))) {
        return false;
      }

      // Check page content for adult indicators
      const indicators = this.scanForAdultIndicators();

      if (indicators.length > 0) {
        console.log('Adult content indicators found:', indicators);
        this.handleBlockedContent('adult', indicators.join(', '));
        return true;
      }
    } catch (error) {
      console.error('Error detecting adult content:', error);
    }

    return false;
  }

  // Phase 4: Scan page for adult content indicators
  scanForAdultIndicators() {
    const indicators = [];

    // Check meta tags
    const metaTags = document.querySelectorAll('meta[name="keywords"], meta[name="description"]');
    metaTags.forEach(meta => {
      const content = meta.getAttribute('content')?.toLowerCase() || '';
      if (this.containsAdultKeywords(content)) {
        indicators.push('meta tags');
      }
    });

    // Check page title
    if (this.containsAdultKeywords(document.title.toLowerCase())) {
      indicators.push('page title');
    }

    // Check for adult-related images (alt text, filenames) - improved algorithm
    const images = document.querySelectorAll('img');
    let adultImageCount = 0;
    images.forEach(img => {
      const alt = img.getAttribute('alt')?.toLowerCase() || '';
      const src = img.getAttribute('src')?.toLowerCase() || '';

      // Skip common legitimate uses of flagged keywords
      const legitimateContexts = [
        'verification', 'account', 'profile', 'business', 'professional',
        'service', 'feature', 'product', 'company', 'corporate', 'official',
        'banking', 'finance', 'payment', 'security', 'identity', 'document'
      ];

      const hasLegitimateContext = legitimateContexts.some(context =>
        alt.includes(context) || src.includes(context)
      );

      // Only count as adult if no legitimate context is found
      if (!hasLegitimateContext && (this.containsAdultKeywords(alt) || this.containsAdultKeywords(src))) {
        adultImageCount++;
      }
    });

    // Increased threshold to reduce false positives
    if (adultImageCount > 5) {
      indicators.push('adult images');
    }

    // Check for adult-related links
    const links = document.querySelectorAll('a');
    let adultLinkCount = 0;
    links.forEach(link => {
      const text = link.textContent?.toLowerCase() || '';
      const href = link.getAttribute('href')?.toLowerCase() || '';

      if (this.containsAdultKeywords(text) || this.containsAdultKeywords(href)) {
        adultLinkCount++;
      }
    });

    if (adultLinkCount > 3) {
      indicators.push('adult links');
    }

    return indicators;
  }

  // Phase 4: Check if text contains adult keywords (improved with context awareness)
  containsAdultKeywords(text) {
    const adultKeywords = [
      'porn', 'sex', 'nude', 'naked', 'xxx', 'erotic', 'nsfw',
      'masturbation', 'orgasm', 'fetish', 'bdsm', 'escort', 'cam girl',
      'live cam', 'webcam', 'strip', 'sexy', 'hot girls'
    ];

    // Exclude 'adult' keyword as it has too many legitimate uses
    // (adult verification, adult account, etc.)

    // Check for legitimate business contexts that should be excluded
    const businessContexts = [
      'verification', 'account', 'profile', 'business', 'professional',
      'service', 'feature', 'product', 'company', 'corporate', 'official',
      'banking', 'finance', 'payment', 'security', 'identity', 'document',
      'age verification', 'adult account', 'adult verification'
    ];

    // If text contains business context, be more restrictive
    const hasBusinessContext = businessContexts.some(context => text.includes(context));

    if (hasBusinessContext) {
      // Only flag truly explicit keywords in business contexts
      const explicitKeywords = [
        'porn', 'nude', 'naked', 'xxx', 'erotic', 'nsfw',
        'masturbation', 'orgasm', 'fetish', 'bdsm'
      ];
      return explicitKeywords.some(keyword => text.includes(keyword));
    }

    return adultKeywords.some(keyword => text.includes(keyword));
  }

  // Phase 4: Handle blocked content detection
  handleBlockedContent(type, details) {
    console.log(`Blocked content detected (${type}):`, details);

    // Create blocking overlay
    this.injectContentBlockingOverlay(type, details);

    // Optionally redirect to blocked page
    setTimeout(() => {
      const blockedPageUrl = chrome.runtime.getURL('blocked/blocked.html') +
        `?blocked=${encodeURIComponent(window.location.href)}&reason=${type}&details=${encodeURIComponent(details)}`;
      window.location.href = blockedPageUrl;
    }, 2000);
  }

  // Phase 4: Inject content blocking overlay
  injectContentBlockingOverlay(type, details) {
    // Remove existing overlay
    const existingOverlay = document.getElementById('focusguard-content-overlay');
    if (existingOverlay) {
      existingOverlay.remove();
    }

    const overlay = document.createElement('div');
    overlay.id = 'focusguard-content-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(220, 38, 127, 0.95);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      backdrop-filter: blur(10px);
    `;

    const typeText = type === 'adult' ? 'Adult Content' : 'Blocked Keywords';
    const icon = type === 'adult' ? '🔞' : '🚫';

    overlay.innerHTML = `
      <div style="text-align: center; max-width: 500px; padding: 40px;">
        <div style="font-size: 64px; margin-bottom: 20px;">${icon}</div>
        <h1 style="font-size: 28px; margin-bottom: 16px; font-weight: 600;">${typeText} Detected</h1>
        <p style="font-size: 18px; margin-bottom: 24px; opacity: 0.9; line-height: 1.5;">
          This page contains ${type === 'adult' ? 'adult content' : 'blocked keywords'} and has been blocked by FocusGuard Pro for your protection.
        </p>
        <div style="background: rgba(255,255,255,0.1); padding: 16px; border-radius: 8px; margin-bottom: 24px;">
          <p style="font-size: 14px; opacity: 0.8;">Detected: ${details}</p>
        </div>
        <button id="focusguard-content-close" style="
          background: rgba(255,255,255,0.2);
          border: 2px solid rgba(255,255,255,0.3);
          color: white;
          padding: 14px 28px;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
        ">Go to Safe Page</button>
      </div>
    `;

    document.body.appendChild(overlay);

    // Add close functionality
    document.getElementById('focusguard-content-close').addEventListener('click', () => {
      window.location.href = 'https://www.google.com';
    });
  }
}

// Initialize content script only if not already initialized
if (!window.focusGuardInitialized) {
  window.focusGuardInitialized = true;

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new FocusGuardContent();
    });
  } else {
    new FocusGuardContent();
  }
}
