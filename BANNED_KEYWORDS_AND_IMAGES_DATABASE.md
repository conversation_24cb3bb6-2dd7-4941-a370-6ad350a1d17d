# FocusGuard Pro - Banned Keywords and Images Database

This document contains all hardcoded banned keywords, blocked domains, and image patterns found in the FocusGuard Pro codebase.

## 📍 Overview

The content filtering system uses multiple layers of protection:
- **Adult Content Sites Database**: Pre-loaded list of adult websites
- **Blocked Keywords**: Terms blocked in search queries and page content
- **Adult Content Patterns**: Regular expressions for domain/URL matching
- **Image Detection**: Keywords for identifying adult images
- **Search Engine Parameters**: Safe search enforcement

---

## 🔞 Adult Content Sites Database

**Location**: `background/background.js` - `loadAdultContentDatabase()` method (lines 188-200)

### Major Adult Content Sites
```javascript
'pornhub.com', 'xvideos.com', 'xnxx.com', 'redtube.com', 'youporn.com',
'tube8.com', 'spankbang.com', 'xhamster.com', 'beeg.com', 'sex.com',
'porn.com', 'chaturbate.com', 'cam4.com', 'livejasmin.com', 'stripchat.com',
'onlyfans.com', 'manyvids.com', 'clips4sale.com', 'iwantclips.com'
```

### Dating/Hookup Sites
```javascript
'adultfriendfinder.com', 'ashley-madison.com', 'fling.com', 'benaughty.com'
```

### Adult Forums and Communities
```javascript
'reddit.com/r/nsfw', 'reddit.com/r/gonewild', 'reddit.com/r/porn'
```

### Image Hosting with Adult Content
```javascript
'imagefap.com', 'motherless.com', 'heavy-r.com'
```

---

## 🚫 Blocked Keywords Database

**Location**: `background/background.js` - `loadDefaultBlockedKeywords()` method (lines 210-221)

### Explicit Terms
```javascript
'porn', 'sex', 'nude', 'naked', 'xxx', 'adult', 'erotic', 'nsfw',
'masturbation', 'orgasm', 'fetish', 'bdsm', 'escort', 'prostitute'
```

### Gambling Terms
```javascript
'casino', 'gambling', 'poker', 'blackjack', 'slots', 'betting',
'lottery', 'jackpot', 'roulette', 'baccarat'
```

### Violence/Harmful Content
```javascript
'suicide', 'self-harm', 'cutting', 'anorexia', 'bulimia'
```

### Drugs
```javascript
'cocaine', 'heroin', 'methamphetamine', 'ecstasy', 'lsd'
```

---

## 🔍 Adult Content Detection Patterns

**Location**: `background/background.js` - `isAdultContent()` method (lines 1011-1014)

### Domain Pattern Matching (Regular Expressions)
```javascript
/porn/i, /sex/i, /xxx/i, /adult/i, /nude/i, /naked/i,
/erotic/i, /cam/i, /escort/i, /hookup/i, /dating/i
```

### URL Path Keywords
**Location**: `background/background.js` - lines 1024-1030
```javascript
'porn', 'sex', 'nude', 'xxx', 'adult', 'nsfw', 'erotic'
```

---

## 🖼️ Image Content Detection Keywords

**Location**: `content/content.js` - `containsAdultKeywords()` method (lines 463-467)

### Adult Image Keywords
```javascript
'porn', 'sex', 'nude', 'naked', 'xxx', 'adult', 'erotic', 'nsfw',
'masturbation', 'orgasm', 'fetish', 'bdsm', 'escort', 'cam girl',
'live cam', 'webcam', 'strip', 'sexy', 'hot girls'
```

---

## 📚 Storage Default Keywords

**Location**: `utils/storage.js` - `getBlockedKeywords()` method (lines 191-193)

### Default Storage Keywords
```javascript
'adult', 'porn', 'xxx', 'sex', 'nude', 'naked', 'erotic'
```

---

## 🔍 Search Engine Safe Search Parameters

**Location**: `background/background.js` - `forceSafeSearch()` method (lines 1070-1080)

### Search Engine Configurations
- **Google**: `safe=strict`
- **Bing**: `adlt=strict`
- **Yahoo**: `vm=r`
- **DuckDuckGo**: `kp=1` (Strict safe search)
- **Yandex**: `family=yes`

### Monitored Search Engines
**Location**: `background/background.js` - lines 1092-1099
```javascript
{ domain: 'google.com', param: 'q' },
{ domain: 'bing.com', param: 'q' },
{ domain: 'yahoo.com', param: 'p' },
{ domain: 'duckduckgo.com', param: 'q' },
{ domain: 'yandex.com', param: 'text' },
{ domain: 'baidu.com', param: 'wd' }
```

---

## 🎯 Smart Redirect Categories

**Location**: `background/background.js` - `getSiteCategory()` method (lines 1295-1327)

### Social Media Sites
```javascript
'facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com',
'snapchat.com', 'tiktok.com', 'reddit.com', 'pinterest.com'
```

### Entertainment Sites
```javascript
'youtube.com', 'netflix.com', 'hulu.com', 'twitch.tv',
'spotify.com', 'soundcloud.com', 'vimeo.com'
```

### Shopping Sites
```javascript
'amazon.com', 'ebay.com', 'etsy.com', 'walmart.com',
'target.com', 'bestbuy.com', 'alibaba.com'
```

### Default Category Redirects
**Location**: `background/background.js` - lines 56-61
```javascript
'Social Media → Productivity': 'notion.so',
'Entertainment → Learning': 'coursera.org',
'Shopping → Finance': 'mint.com'
```

---

## 🛡️ Content Filtering Implementation

### Detection Methods
1. **Domain Matching**: Exact domain comparison against adult sites database
2. **Pattern Matching**: Regular expression matching on domains and URLs
3. **Keyword Scanning**: Page content and search query analysis
4. **Image Analysis**: Alt text and src attribute scanning
5. **Meta Tag Analysis**: Page metadata examination

### Protection Levels
- **Unbypassable**: Adult content blocks cannot be overridden (except with 1-hour countdown)
- **Temporary Disable**: 10-minute temporary disable with auto re-enable
- **Safe Search**: Forced safe search across all major search engines

---

## 📝 Notes

- All keywords are processed in lowercase for case-insensitive matching
- The adult content database is described as a "sample" and would be "more comprehensive in production"
- Custom keywords can be added by users and are merged with default keywords
- The system monitors dynamic content changes on pages
- Content filtering cannot be bypassed by temporary unblocks (except for the countdown mechanism)

---

## 🔧 Technical Implementation

### File Locations
- **Main Logic**: `background/background.js`
- **Content Scanning**: `content/content.js`
- **Storage Defaults**: `utils/storage.js`
- **Configuration**: `manifest.json`
- **Rules**: `rules.json`

### Storage Keys
- `adultContentSites`: Set of blocked adult domains
- `blockedKeywords`: Set of blocked keywords
- `contentFiltering`: Configuration object
- `customKeywords`: User-added keywords array

---

*Last Updated: Analysis performed on current codebase*
*Total Adult Sites: 20+ domains*
*Total Default Keywords: 21 terms across 4 categories*
*Total Image Keywords: 15 terms*
